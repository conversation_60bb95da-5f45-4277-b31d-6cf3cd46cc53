<template>
  <div class="api-key-list">
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd" plain>
        <el-icon><Plus /></el-icon>
        创建API密钥
      </el-button>
      <div class="search-area">
        <el-select v-model="searchForm.status" placeholder="状态" clearable @change="loadAPIKeys">
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="2" />
        </el-select>
        <el-input
          v-model="searchForm.name"
          placeholder="搜索密钥名称"
          clearable
          @keyup.enter="loadAPIKeys"
          @clear="loadAPIKeys"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <el-card class="list-card" shadow="never">
      <el-table :data="apiKeys" v-loading="loading" stripe>
        <el-table-column prop="secret_id" label="SecretId" min-width="280" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="secret-id-cell">
              <span class="secret-id">{{ row.secret_id }}</span>
              <el-button
                type="primary"
                link
                size="small"
                @click="copyToClipboard(row.secret_id)"
              >
                复制
              </el-button>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="密钥名称" width="150" />

        <el-table-column label="权限" width="120">
          <template #default="{ row }">
            <div class="permissions">
              <el-tag v-if="getPermissions(row.permissions).recharge" size="small" type="success" effect="light">
                充值
              </el-tag>
              <el-tag v-if="getPermissions(row.permissions).query" size="small" type="info" effect="light">
                查询
              </el-tag>
              <span v-if="!getPermissions(row.permissions).recharge && !getPermissions(row.permissions).query" class="no-permission">
                无权限
              </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'" effect="light">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="last_used_at" label="最后使用" width="180">
          <template #default="{ row }">
            <div v-if="row.last_used_at">
              <div>{{ formatDateTime(row.last_used_at) }}</div>
              <div class="time-ago">{{ getTimeAgo(row.last_used_at) }}</div>
            </div>
            <span v-else class="never-used">从未使用</span>
          </template>
        </el-table-column>

        <el-table-column prop="expires_at" label="过期时间" width="180">
          <template #default="{ row }">
            {{ row.expires_at ? formatDateTime(row.expires_at) : '永不过期' }}
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="showSecretKey(row)">查看密钥</el-button>
            <el-button
              :type="row.status === 1 ? 'warning' : 'success'"
              link
              @click="toggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" link @click="deleteAPIKey(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <el-dialog
      v-model="dialogVisible"
      title="创建API密钥"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="密钥名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入密钥名称" />
        </el-form-item>

        <el-form-item label="权限配置" prop="permissions">
          <el-checkbox-group v-model="selectedPermissions">
            <el-checkbox label="recharge">充值权限</el-checkbox>
            <el-checkbox label="query">查询权限</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="过期时间">
          <el-date-picker
            v-model="form.expires_at"
            type="datetime"
            placeholder="选择过期时间（可选）"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">创建</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="secretDialogVisible"
      title="API密钥详情"
      width="600px"
      destroy-on-close
    >
      <div v-if="currentAPIKey" class="secret-detail">
        <div class="secret-item">
          <label>SecretId:</label>
          <div class="secret-value">
            <code>{{ currentAPIKey.secret_id }}</code>
            <el-button
              type="primary"
              link
              size="small"
              @click="copyToClipboard(currentAPIKey.secret_id)"
            >
              复制
            </el-button>
          </div>
        </div>

        <div class="secret-item">
          <label>SecretKey:</label>
          <div class="secret-value">
            <code>{{ displaySecretKey }}</code>
            <el-button
              v-if="!showFullSecretKey"
              type="primary"
              link
              size="small"
              @click="showFullSecretKey = true"
            >
              显示
            </el-button>
            <el-button
              v-else
              type="primary"
              link
              size="small"
              @click="copyToClipboard(currentAPIKey.secret_key)"
            >
              复制
            </el-button>
          </div>
        </div>

        <el-alert
          title="安全提醒"
          type="warning"
          :closable="false"
          show-icon
        >
          <p>请妥善保管您的SecretKey，不要在客户端代码中硬编码。</p>
          <p>SecretKey仅在创建时完整显示，请及时保存。</p>
        </el-alert>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="secretDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import api, { type ThirdAPIKey, type CreateAPIKeyData, type APIKeyPermissions } from '@/api'

// 响应式数据
const loading = ref(false)
const apiKeys = ref<ThirdAPIKey[]>([])
const dialogVisible = ref(false)
const secretDialogVisible = ref(false)
const currentAPIKey = ref<ThirdAPIKey | null>(null)
const showFullSecretKey = ref(false)
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  status: undefined as number | undefined
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 创建表单
const form = reactive<CreateAPIKeyData>({
  name: '',
  permissions: {
    recharge: false,
    query: false
  },
  expires_at: undefined
})

const selectedPermissions = ref<string[]>([])

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入密钥名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const displaySecretKey = computed(() => {
  if (!currentAPIKey.value) return ''
  if (showFullSecretKey.value) {
    return currentAPIKey.value.secret_key
  }
  return '***'
})

// 加载API密钥列表
const loadAPIKeys = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...searchForm
    }

    const response = await api.getAPIKeyList(params)
    apiKeys.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('加载API密钥列表失败')
  } finally {
    loading.value = false
  }
}

// 处理添加
const handleAdd = () => {
  dialogVisible.value = true
  resetForm()
}

// 重置表单
const resetForm = () => {
  form.name = ''
  form.permissions = { recharge: false, query: false }
  form.expires_at = undefined
  selectedPermissions.value = []
  showFullSecretKey.value = false
  formRef.value?.resetFields()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  loadAPIKeys()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadAPIKeys()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构建权限对象
    form.permissions = {
      recharge: selectedPermissions.value.includes('recharge'),
      query: selectedPermissions.value.includes('query')
    }

    // 检查是否至少选择了一个权限
    if (!form.permissions.recharge && !form.permissions.query) {
      ElMessage.warning('请至少选择一个权限')
      return
    }

    const response = await api.createAPIKey(form)

    ElMessage.success('API密钥创建成功')
    dialogVisible.value = false

    // 显示新创建的密钥
    currentAPIKey.value = response.data
    showFullSecretKey.value = true // 创建时默认显示完整密钥
    secretDialogVisible.value = true

    // 刷新列表
    loadAPIKeys()
  } catch (error) {
    ElMessage.error('创建API密钥失败')
  }
}

// 查看密钥详情
const showSecretKey = async (apiKey: ThirdAPIKey) => {
  try {
    // 调用详情接口获取完整的SecretKey
    const response = await api.getAPIKeyDetail(apiKey.secret_id)
    currentAPIKey.value = response.data
    showFullSecretKey.value = false // 查看时默认隐藏密钥
    secretDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取密钥详情失败')
  }
}

// 切换状态
const toggleStatus = async (apiKey: ThirdAPIKey) => {
  const newStatus = apiKey.status === 1 ? 2 : 1
  const action = newStatus === 1 ? '启用' : '禁用'

  try {
    await ElMessageBox.confirm(
      `确定要${action}此API密钥吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.updateAPIKeyStatus(apiKey.secret_id, newStatus)
    ElMessage.success(`${action}成功`)
    loadAPIKeys()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除API密钥
const deleteAPIKey = async (apiKey: ThirdAPIKey) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除API密钥 "${apiKey.name}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.deleteAPIKey(apiKey.secret_id)
    ElMessage.success('删除成功')
    loadAPIKeys()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 解析权限
const getPermissions = (permissionsStr: string): APIKeyPermissions => {
  try {
    const parsed = JSON.parse(permissionsStr)
    // 适配后端返回的格式（首字母大写）
    return {
      recharge: parsed.Recharge || parsed.recharge || false,
      query: parsed.Query || parsed.query || false
    }
  } catch {
    return { recharge: false, query: false }
  }
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 计算时间差
const getTimeAgo = (dateStr: string) => {
  const now = new Date()
  const date = new Date(dateStr)
  const diffMs = now.getTime() - date.getTime()

  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 30) {
    return `${diffDays}天前`
  } else {
    return `${Math.floor(diffDays / 30)}个月前`
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadAPIKeys()
})
</script>

<style scoped>
.api-key-list {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-area {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-area .el-select {
  width: 120px;
}

.search-area .el-input {
  width: 240px;
}

.list-card {
  border: 1px solid #ebeef5;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.secret-id-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.secret-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
  flex: 1;
}

.permissions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.no-permission {
  color: #909399;
  font-size: 12px;
}

.secret-detail {
  padding: 10px 0;
}

.secret-item {
  margin-bottom: 20px;
}

.secret-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #303133;
}

.secret-value {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.secret-value code {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #606266;
  word-break: break-all;
  background: none;
  padding: 0;
}

.el-alert {
  margin-top: 20px;
}

.el-alert p {
  margin: 4px 0;
}

.dialog-footer {
  text-align: right;
}

.time-ago {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.never-used {
  color: #909399;
  font-style: italic;
}
</style>
