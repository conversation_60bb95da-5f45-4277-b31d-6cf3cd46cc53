package handler

import (
	"crypto/rand"
	"goadmin/pkg/logger"
	"math/big"
	"time"

	"github.com/gin-gonic/gin"

	"goadmin/internal/constant"
	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// CardKeyRequest 卡密请求
type CardKeyRequest struct {
	CardKey  string `json:"card_key" binding:"required"`
	DeviceID string `json:"device_id" binding:"required"`
}

// CardKeyDetailRequest 卡密详情请求
type CardKeyDetailRequest struct {
	CardKey  string `json:"card_key" binding:"required"`
	Page     int    `json:"page" binding:"required,min=1"`
	PageSize int    `json:"page_size" binding:"required,min=1,max=100"`
}

// GenerateCardKeyRequest 生成卡密请求
type GenerateCardKeyRequest struct {
	Type       int8   `json:"type" binding:"required"`
	Value      int    `json:"value" binding:"required"`
	Prefix     string `json:"prefix"`
	Length     int    `json:"length" binding:"required"`
	Count      int    `json:"count" binding:"required,min=1,max=1000"`
	MaxDevices int    `json:"max_devices" binding:"required,min=1"` // 支持的最大设备数量
}

// ListCardKeysRequest 列出卡密请求
type ListCardKeysRequest struct {
	Page     int    `json:"page" binding:"required,min=1"`
	PageSize int    `json:"page_size" binding:"required,min=1,max=100"`
	Status   int8   `json:"status"`
	Type     int8   `json:"type"`
	CardKey  string `json:"card_key"`
}

// RechargeCardKeyRequest 以卡充卡请求
type RechargeCardKeyRequest struct {
	TargetCardKey string `json:"target_card_key" binding:"required"` // 目标卡密
	NewCardKey    string `json:"new_card_key" binding:"required"`    // 新卡密
}

type CardKeyHandler struct {
	cardService    *service.CardKeyService
	bindingService *service.BindingService
	jwtService     *service.JWTService
	userLogService *service.UserLogService
}

func NewCardKeyHandler(cardService *service.CardKeyService, bindingService *service.BindingService, jwtService *service.JWTService, userLogService *service.UserLogService) *CardKeyHandler {
	return &CardKeyHandler{
		cardService:    cardService,
		bindingService: bindingService,
		jwtService:     jwtService,
		userLogService: userLogService,
	}
}

// Login 卡密登录
func (h *CardKeyHandler) Login(c *gin.Context) {
	var req CardKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	// 获取卡密信息
	card, err := h.cardService.GetCardKey(c.Request.Context(), req.CardKey)
	if err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	// 检查卡密是否过期
	if card.Status == constant.CardKeyStatusExpired {
		response.Fail(c, response.CARD_EXPIRED, "卡密已过期")
		return
	}

	// 检查设备是否已绑定
	binding, _, err := h.bindingService.ValidateBinding(req.CardKey, req.DeviceID)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "设备未绑定")
		return
	}
	if binding == nil {
		response.Fail(c, response.ERROR, "设备未绑定")
		return
	}

	// 更新设备活跃状态
	if err := h.bindingService.UpdateDeviceStatus(c.Request.Context(), req.CardKey, req.DeviceID, true); err != nil {
		response.Fail(c, response.ERROR, err.Error())
		return
	}

	// 异步记录登录日志
	h.userLogService.CreateLoginLog(req.CardKey, req.DeviceID)

	// 生成客户端token
	token, err := h.jwtService.GenerateClientToken(req.CardKey, req.DeviceID)
	if err != nil {
		response.Fail(c, response.ERROR, "生成token失败")
		return
	}

	// 格式化时间字段
	cardResponse := map[string]interface{}{
		"card_key":    card.CardKey,
		"status":      card.Status,
		"type":        card.Type,
		"value":       card.Value,
		"max_devices": card.MaxDevices,
		"created_at":  card.CreatedAt.Format("2006-01-02 15:04:05"),
		"expired_at":  "",
		"used_at":     "",
	}
	if !card.ExpiredAt.IsZero() {
		cardResponse["expired_at"] = card.ExpiredAt.Format("2006-01-02 15:04:05")
	}
	if !card.UsedAt.IsZero() {
		cardResponse["used_at"] = card.UsedAt.Format("2006-01-02 15:04:05")
	}

	response.Success(c, gin.H{
		"card":  cardResponse,
		"token": token,
	})
}

// GenerateCardKey 生成卡密
func (h *CardKeyHandler) GenerateCardKey(c *gin.Context) {
	var req GenerateCardKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	cardKeys := make([]string, 0, req.Count)
	for i := 0; i < req.Count; i++ {
		// 生成卡密
		cardKey := req.Prefix + generateRandomString(req.Length)
		if err := h.cardService.GenerateCardKey(c.Request.Context(), cardKey, req.Type, req.Value, req.MaxDevices, time.Time{}); err != nil {
			response.Fail(c, response.ERROR, "生成卡密失败")
			return
		}
		cardKeys = append(cardKeys, cardKey)
	}

	response.Success(c, gin.H{
		"card_keys": cardKeys,
	})
}

// generateRandomString 生成指定长度的随机字符串
func generateRandomString(length int) string {
	const charset = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	charsetLength := big.NewInt(int64(len(charset)))
	result := make([]byte, length)

	for i := 0; i < length; i++ {
		n, err := rand.Int(rand.Reader, charsetLength)
		if err != nil {
			// 如果生成随机数失败，使用时间戳作为备选方案
			result[i] = charset[time.Now().UnixNano()%int64(len(charset))]
			continue
		}
		result[i] = charset[n.Int64()]
	}

	return string(result)
}

// ListCardKeys 列出卡密
func (h *CardKeyHandler) ListCardKeys(c *gin.Context) {
	var req ListCardKeysRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(c, "ListCardKeys error: %v", err)
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	cards, total, err := h.cardService.ListCardKeys(c.Request.Context(), req.Page, req.PageSize, req.Status, req.Type, req.CardKey)
	if err != nil {
		response.Fail(c, response.ERROR, "获取卡密列表失败")
		return
	}

	response.Success(c, gin.H{
		"total": total,
		"items": cards,
	})
}

// DeleteCardKey 删除卡密
func (h *CardKeyHandler) DeleteCardKey(c *gin.Context) {
	var req struct {
		CardKey string `json:"card_key" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	if err := h.cardService.BatchDeleteCardKeys(c.Request.Context(), []string{req.CardKey}); err != nil {
		response.Fail(c, response.ERROR, "删除卡密失败")
		return
	}

	response.Success(c, nil)
}

// BatchDeleteCardKeys 批量删除卡密
func (h *CardKeyHandler) BatchDeleteCardKeys(c *gin.Context) {
	var req struct {
		CardKeys []string `json:"card_keys" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	if err := h.cardService.BatchDeleteCardKeys(c.Request.Context(), req.CardKeys); err != nil {
		response.Fail(c, response.ERROR, "批量删除卡密失败")
		return
	}

	response.Success(c, nil)
}

// GetCardKeyWithDevices 获取卡密及其绑定的设备
func (h *CardKeyHandler) GetCardKeyWithDevices(c *gin.Context) {
	var req CardKeyDetailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	card, bindings, total, err := h.cardService.GetCardKeyWithDevices(c.Request.Context(), req.CardKey, req.Page, req.PageSize)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取卡密信息失败")
		return
	}

	response.Success(c, gin.H{
		"card":     card,
		"bindings": bindings,
		"total":    total,
	})
}

// RechargeCardKey 以卡充卡
func (h *CardKeyHandler) RechargeCardKey(c *gin.Context) {
	var req RechargeCardKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	if err := h.cardService.RechargeCardKey(c.Request.Context(), req.TargetCardKey, req.NewCardKey); err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "卡密充值失败")
		return
	}

	response.Success(c, nil)
}
