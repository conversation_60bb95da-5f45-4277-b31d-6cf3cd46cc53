package handler

import (
	"encoding/json"
	"github.com/gin-gonic/gin"

	"goadmin/internal/service"
	"goadmin/pkg/response"
)

// CardConfigHandler 卡密配置处理器
type CardConfigHandler struct {
	cardConfigService *service.CardConfigService
}

// NewCardConfigHandler 创建卡密配置处理器
func NewCardConfigHandler(cardConfigService *service.CardConfigService) *CardConfigHandler {
	return &CardConfigHandler{
		cardConfigService: cardConfigService,
	}
}

// UploadConfigRequest 上传配置请求
type UploadConfigRequest struct {
	CardKey string      `json:"card_key" binding:"required"`
	Config  interface{} `json:"config" binding:"required"`
}

// UploadConfig 上传配置
func (h *CardConfigHandler) UploadConfig(c *gin.Context) {
	var req UploadConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	cardConfig, err := h.cardConfigService.UploadConfig(c.Request.Context(), req.CardKey, req.Config)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "上传配置失败")
		return
	}

	response.Success(c, gin.H{
		"card_key": cardConfig.CardKey,
		"message":  "配置上传成功",
	})
}

// GetConfigRequest 获取配置请求
type GetConfigRequest struct {
	CardKey string `json:"card_key" binding:"required"`
}

// GetConfig 获取配置
func (h *CardConfigHandler) GetConfig(c *gin.Context) {
	var req GetConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, response.INVALID_PARAMS, "无效的请求参数")
		return
	}

	cardConfig, err := h.cardConfigService.GetConfig(c.Request.Context(), req.CardKey)
	if err != nil {
		if e, ok := err.(*response.Error); ok {
			response.Fail(c, e.Code, e.Message)
			return
		}
		response.Fail(c, response.ERROR, "获取配置失败")
		return
	}

	// 解析配置JSON
	var config interface{}
	if err := json.Unmarshal([]byte(cardConfig.Config), &config); err != nil {
		response.Fail(c, response.ERROR, "配置解析失败")
		return
	}

	response.Success(c, gin.H{
		"card_key":   cardConfig.CardKey,
		"config":     config,
		"updated_at": cardConfig.UpdatedAt,
	})
}
