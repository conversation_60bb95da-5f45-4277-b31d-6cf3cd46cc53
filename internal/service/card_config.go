package service

import (
	"context"
	"encoding/json"

	"gorm.io/gorm"

	"goadmin/internal/model"
	"goadmin/pkg/response"
)

// CardConfigService 卡密配置服务
type CardConfigService struct {
	db *gorm.DB
}

// NewCardConfigService 创建卡密配置服务
func NewCardConfigService(db *gorm.DB) *CardConfigService {
	return &CardConfigService{
		db: db,
	}
}

// UploadConfig 上传配置
func (s *CardConfigService) UploadConfig(ctx context.Context, cardKey string, config interface{}) (*model.CardConfig, error) {
	// 序列化配置
	configJSON, err := json.Marshal(config)
	if err != nil {
		return nil, response.NewError(response.ERROR, "配置序列化失败")
	}

	// 查找现有配置
	var cardConfig model.CardConfig
	err = s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&cardConfig).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新配置
		cardConfig = model.CardConfig{
			CardKey: cardKey,
			Config:  string(configJSON),
		}
		if err := s.db.WithContext(ctx).Create(&cardConfig).Error; err != nil {
			return nil, response.NewError(response.ERROR, "创建配置失败")
		}
	} else if err != nil {
		return nil, response.NewError(response.ERROR, "查询配置失败")
	} else {
		// 直接覆盖现有配置
		cardConfig.Config = string(configJSON)
		if err := s.db.WithContext(ctx).Save(&cardConfig).Error; err != nil {
			return nil, response.NewError(response.ERROR, "更新配置失败")
		}
	}

	return &cardConfig, nil
}

// GetConfig 获取配置
func (s *CardConfigService) GetConfig(ctx context.Context, cardKey string) (*model.CardConfig, error) {
	// 查找配置
	var cardConfig model.CardConfig
	err := s.db.WithContext(ctx).Where("card_key = ?", cardKey).First(&cardConfig).Error

	if err == gorm.ErrRecordNotFound {
		return nil, response.NewError(response.ERROR, "配置不存在")
	} else if err != nil {
		return nil, response.NewError(response.ERROR, "查询配置失败")
	}

	return &cardConfig, nil
}
