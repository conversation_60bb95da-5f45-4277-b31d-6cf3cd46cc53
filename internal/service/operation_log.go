package service

import (
	"encoding/json"
	"goadmin/internal/model"

	"gorm.io/gorm"
)

// OperationLogService 操作日志服务
type OperationLogService struct {
	db *gorm.DB
}

// NewOperationLogService 创建操作日志服务实例
func NewOperationLogService(db *gorm.DB) *OperationLogService {
	return &OperationLogService{db: db}
}

// CreateLog 创建操作日志
func (s *OperationLogService) CreateLog(log *model.OperationLog) error {
	return s.db.Create(log).Error
}

// CreateLogFromRequest 从请求信息创建日志
func (s *OperationLogService) CreateLogFromRequest(path, method string, params interface{}, response interface{}, ip, userAgent string, status int, code int) error {
	// 将请求参数转换为JSON字符串
	paramsBytes, err := json.Marshal(params)
	if err != nil {
		paramsBytes = []byte("{}")
	}

	// 将响应结果转换为JSON字符串
	responseBytes, err := json.Marshal(response)
	if err != nil {
		responseBytes = []byte("{}")
	}

	// 创建日志记录
	log := &model.OperationLog{
		Path:      path,
		Method:    method,
		Params:    string(paramsBytes),
		Response:  string(responseBytes),
		IP:        ip,
		UserAgent: userAgent,
		Status:    status,
		Code:      code,
	}

	return s.CreateLog(log)
}
