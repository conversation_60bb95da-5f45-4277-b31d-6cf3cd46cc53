package service

import (
	"context"
	"strconv"
	"time"

	"gorm.io/gorm"

	"goadmin/internal/constant"
	"goadmin/internal/model"
	"goadmin/pkg/logger"
	"goadmin/pkg/response"
)

type BindingService struct {
	db            *gorm.DB
	cardService   *CardKeyService
	configService *ConfigService
}

func NewBindingService(db *gorm.DB, cardService *CardKeyService, configService *ConfigService) *BindingService {
	return &BindingService{
		db:            db,
		cardService:   cardService,
		configService: configService,
	}
}

// BindDevice 绑定设备
func (s *BindingService) BindDevice(ctx context.Context, cardKey, deviceID string) error {
	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 检查卡密是否存在
		var card model.CardKey
		if err := tx.Where("card_key = ?", cardKey).First(&card).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return response.NewError(response.CARD_NOT_FOUND, "卡密不存在")
			}
			return response.NewError(response.ERROR, "查询卡密失败")
		}

		// 检查卡密状态
		if card.Status == constant.CardKeyStatusExpired {
			return response.NewError(response.CARD_EXPIRED, "卡密已过期")
		}

		// 检查是否已经绑定过该设备
		var existingBinding model.CardDeviceBinding
		err := tx.Where("card_key = ? AND device_id = ?", cardKey, deviceID).First(&existingBinding).Error
		if err == nil {
			// 如果已经绑定过该设备，直接返回成功
			return nil
		}

		// 检查已绑定的设备数量是否达到上限
		var bindingCount int64
		if err := tx.Model(&model.CardDeviceBinding{}).Where("card_key = ?", cardKey).Count(&bindingCount).Error; err != nil {
			return response.NewError(response.ERROR, "检查绑定状态失败")
		}

		if bindingCount >= int64(card.MaxDevices) {
			return response.NewError(response.BIND_LIMIT, "已达到最大设备绑定数量,请解绑或者解绑全部")
		}

		// 创建绑定关系
		now := time.Now()
		binding := &model.CardDeviceBinding{
			CardKey:    cardKey,
			DeviceID:   deviceID,
			Status:     constant.DeviceStatusOnline,
			LastActive: &now,
		}

		if err := tx.Create(binding).Error; err != nil {
			return response.NewError(response.ERROR, "绑定设备失败")
		}

		// 仅在卡密首次激活（未使用状态）时更新卡密状态和过期时间
		if card.Status == constant.CardKeyStatusUnused {
			updates := map[string]interface{}{
				"status":  constant.CardKeyStatusUsed,
				"used_at": time.Now(),
			}

			// 根据卡密类型计算过期时间
			if card.Type == constant.CardKeyTypeTime { // 时间卡
				expiredAt := time.Now().Add(time.Duration(card.Value) * 24 * time.Hour)
				updates["expired_at"] = expiredAt
			}

			if err := tx.Model(&model.CardKey{}).Where("card_key = ?", cardKey).Updates(updates).Error; err != nil {
				return response.NewError(response.ERROR, "更新卡密状态失败")
			}
		}

		return nil
	})
}

// UnbindDevice 解绑设备
func (s *BindingService) UnbindDevice(ctx context.Context, cardKey string, deviceIDs []string, isAdmin bool) error {
	// 获取系统配置的解绑扣除时长，默认12小时
	penaltyHours := 12
	if config, err := s.configService.GetConfig(ctx, "unbind_penalty_hours"); err == nil && config != nil {
		if hours, err := strconv.Atoi(config.Value); err == nil {
			penaltyHours = hours
		}
	}

	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 获取卡密信息
		var card model.CardKey
		if err := tx.Where("card_key = ?", cardKey).First(&card).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return response.NewError(response.CARD_NOT_FOUND, "卡密不存在")
			}
			return response.NewError(response.ERROR, "查询卡密失败")
		}

		// 检查卡密状态
		if card.Status == constant.CardKeyStatusExpired {
			return response.NewError(response.CARD_EXPIRED, "卡密已过期")
		}

		// 查找所有要解绑的设备
		var bindings []model.CardDeviceBinding
		if err := tx.Where("card_key = ? AND device_id IN ?", cardKey, deviceIDs).Find(&bindings).Error; err != nil {
			return err
		}

		// 如果没有找到任何绑定关系，返回错误
		if len(bindings) == 0 {
			return response.NewError(response.BIND_NOT_FOUND, "未找到绑定关系")
		}

		// 非管理员操作才扣除时长
		if !isAdmin {
			// 计算扣除时长（固定扣除，不乘以设备数量）
			penaltyDuration := time.Duration(penaltyHours) * time.Hour
			newExpiredAt := card.ExpiredAt.Add(-penaltyDuration)

			// 更新卡密过期时间
			if err := tx.Model(&card).Update("expired_at", newExpiredAt).Error; err != nil {
				return err
			}
		}

		// 删除绑定关系
		if err := tx.Delete(&model.CardDeviceBinding{}, "card_key = ? AND device_id IN ?", cardKey, deviceIDs).Error; err != nil {
			return err
		}

		return nil
	})
}

// UnbindAllDevices 解绑所有设备
func (s *BindingService) UnbindAllDevices(ctx context.Context, cardKey string, isAdmin bool) error {
	// 获取系统配置的解绑扣除时长，默认12小时
	penaltyHours := 12
	if config, err := s.configService.GetConfig(ctx, "unbind_penalty_hours"); err == nil && config != nil {
		if hours, err := strconv.Atoi(config.Value); err == nil {
			penaltyHours = hours
		}
	}

	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 获取卡密信息
		var card model.CardKey
		if err := tx.Where("card_key = ?", cardKey).First(&card).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return response.NewError(response.CARD_NOT_FOUND, "卡密不存在")
			}
			return response.NewError(response.ERROR, "查询卡密失败")
		}

		// 检查卡密状态
		if card.Status == constant.CardKeyStatusExpired {
			return response.NewError(response.CARD_EXPIRED, "卡密已过期")
		}

		// 查找所有绑定的设备
		var bindings []model.CardDeviceBinding
		if err := tx.Where("card_key = ?", cardKey).Find(&bindings).Error; err != nil {
			return err
		}

		// 如果没有找到任何绑定关系，返回错误
		if len(bindings) == 0 {
			return response.NewError(response.BIND_NOT_FOUND, "未找到绑定关系")
		}

		// 非管理员操作才扣除时长
		if !isAdmin {
			// 计算扣除时长（固定扣除，不乘以设备数量）
			penaltyDuration := time.Duration(penaltyHours) * time.Hour
			newExpiredAt := card.ExpiredAt.Add(-penaltyDuration)

			// 更新卡密过期时间
			if err := tx.Model(&card).Update("expired_at", newExpiredAt).Error; err != nil {
				return err
			}
		}

		// 删除所有绑定关系
		if err := tx.Delete(&model.CardDeviceBinding{}, "card_key = ?", cardKey).Error; err != nil {
			return err
		}

		return nil
	})
}

// GetBinding 获取绑定信息
func (s *BindingService) GetBinding(id uint) (*model.CardDeviceBinding, error) {
	var binding model.CardDeviceBinding
	if err := s.db.First(&binding, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, response.NewError(response.BIND_NOT_FOUND, "未绑定卡密")
		}
		return nil, response.NewError(response.ERROR, "获取绑定信息失败")
	}
	return &binding, nil
}

// ValidateBinding 验证绑定关系
func (s *BindingService) ValidateBinding(cardKey, deviceID string) (*model.CardDeviceBinding, *model.CardKey, error) {
	var binding model.CardDeviceBinding
	if err := s.db.Preload("Card").Where("card_key = ? AND device_id = ?", cardKey, deviceID).First(&binding).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil, response.NewError(response.BIND_NOT_FOUND, "未绑定卡密")
		}
		return nil, nil, response.NewError(response.ERROR, "获取绑定信息失败")
	}

	return &binding, binding.Card, nil
}

// ListBindings 获取绑定列表
func (s *BindingService) ListBindings(page, pageSize int, cardKey, deviceID string) ([]*model.CardDeviceBinding, int64, error) {
	var bindings []*model.CardDeviceBinding
	var total int64

	query := s.db.Model(&model.CardDeviceBinding{})
	if cardKey != "" {
		query = query.Where("card_key LIKE ?", "%"+cardKey+"%")
	}
	if deviceID != "" {
		query = query.Where("device_id LIKE ?", "%"+deviceID+"%")
	}

	query.Count(&total)
	if err := query.Preload("Card").Offset((page - 1) * pageSize).Limit(pageSize).Find(&bindings).Error; err != nil {
		return nil, 0, response.NewError(response.ERROR, "获取绑定列表失败")
	}

	return bindings, total, nil
}

// GetRecentBindings 获取最近绑定记录
func (s *BindingService) GetRecentBindings() ([]*model.CardDeviceBinding, error) {
	var bindings []*model.CardDeviceBinding
	if err := s.db.Order("created_at DESC").Limit(5).Find(&bindings).Error; err != nil {
		return nil, response.NewError(response.ERROR, "获取最近绑定记录失败")
	}
	return bindings, nil
}

// GetStats 获取统计信息
func (s *BindingService) GetStats() (map[string]interface{}, error) {
	var totalCardKeys, usedCardKeys, expiredCardKeys, totalBindings, activeDevices int64
	var availableBindings int64

	// 总卡密数
	if err := s.db.Model(&model.CardKey{}).Count(&totalCardKeys).Error; err != nil {
		return nil, err
	}

	// 已使用卡密数
	if err := s.db.Model(&model.CardKey{}).Where("status = ?", constant.CardKeyStatusUsed).Count(&usedCardKeys).Error; err != nil {
		return nil, err
	}

	// 过期卡密数
	if err := s.db.Model(&model.CardKey{}).Where("status = ?", constant.CardKeyStatusExpired).Count(&expiredCardKeys).Error; err != nil {
		return nil, err
	}

	// 计算可用绑定数（已使用卡密的最大设备数之和）
	if err := s.db.Model(&model.CardKey{}).
		Where("status = ?", constant.CardKeyStatusUsed).
		Select("SUM(max_devices) as total").
		Scan(&availableBindings).Error; err != nil {
		return nil, err
	}

	// 当前绑定设备总数（只统计已使用状态的卡密的设备）
	if err := s.db.Model(&model.CardDeviceBinding{}).
		Joins("JOIN card_keys ON card_device_bindings.card_key = card_keys.card_key").
		Where("card_keys.status = ?", constant.CardKeyStatusUsed).
		Count(&totalBindings).Error; err != nil {
		return nil, err
	}

	// 当前在线设备数（只统计已使用状态的卡密的设备）
	if err := s.db.Model(&model.CardDeviceBinding{}).
		Joins("JOIN card_keys ON card_device_bindings.card_key = card_keys.card_key").
		Where("card_device_bindings.status = ? AND card_device_bindings.last_active IS NOT NULL", constant.DeviceStatusOnline).
		Where("card_keys.status = ?", constant.CardKeyStatusUsed).
		Count(&activeDevices).Error; err != nil {
		return nil, err
	}

	// 获取最近30天的每日活跃设备数（从登录日志统计）
	var dailyStats []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	if err := s.db.Raw(`
		SELECT 
			DATE_FORMAT(l.created_at, '%m-%d') as date,
			COUNT(DISTINCT l.device_id) as count
		FROM login_logs l
		JOIN card_keys k ON l.card_key = k.card_key
		WHERE l.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
		AND k.status = ?
		GROUP BY DATE(l.created_at)
		ORDER BY DATE(l.created_at) ASC
	`, constant.CardKeyStatusUsed).Scan(&dailyStats).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"totalCardKeys":     totalCardKeys,
		"usedCardKeys":      usedCardKeys,
		"expiredCardKeys":   expiredCardKeys,
		"availableBindings": availableBindings,
		"totalBindings":     totalBindings,
		"activeDevices":     activeDevices,
		"dailyStats":        dailyStats,
	}, nil
}

// UpdateDeviceStatus 更新设备状态
func (s *BindingService) UpdateDeviceStatus(ctx context.Context, cardKey string, deviceID string, isOnline bool) error {
	status := constant.DeviceStatusOffline
	if isOnline {
		status = constant.DeviceStatusOnline
	}

	updates := map[string]interface{}{
		"status":      status,
		"last_active": time.Now(),
	}

	result := s.db.Model(&model.CardDeviceBinding{}).
		Where("card_key = ? AND device_id = ?", cardKey, deviceID).
		Updates(updates)

	if result.Error != nil {
		logger.Error(ctx, "更新设备状态失败: %v", result.Error)
		return response.NewError(response.ERROR, "更新设备状态失败")
	}
	return nil
}

// CheckInactiveDevices 检查不活跃设备
func (s *BindingService) CheckInactiveDevices(inactiveHours int) error {
	inactiveTime := time.Now().Add(-time.Duration(inactiveHours) * time.Hour)

	// 更新超过指定时间未活跃的设备状态为离线
	result := s.db.Model(&model.CardDeviceBinding{}).
		Where("status = ? AND last_active < ?", constant.DeviceStatusOnline, inactiveTime).
		Updates(map[string]interface{}{
			"status": constant.DeviceStatusOffline,
		})

	if result.Error != nil {
		return response.NewError(response.ERROR, "更新设备状态失败")
	}

	return nil
}
