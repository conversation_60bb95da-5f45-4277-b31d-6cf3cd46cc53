package middleware

import (
	"bytes"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"goadmin/internal/service"
	"io"
	"net/http"
	"strings"
)

// OperationLog 操作日志中间件
func OperationLog(operationLogService *service.OperationLogService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 不记录OPTIONS请求
		if c.Request.Method == http.MethodOptions {
			c.Next()
			return
		}

		// 获取请求信息
		path := c.Request.URL.Path
		method := c.Request.Method
		ip := c.ClientIP()
		userAgent := c.Request.UserAgent()

		// 获取请求参数
		var params interface{}
		if c.Request.Method != http.MethodGet {
			body, err := c.GetRawData()
			if err == nil {
				// 将body重新设置回Request，因为GetRawData会消耗掉body
				c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
				// 尝试解析JSON
				if err := json.Unmarshal(body, &params); err != nil {
					// 如果不是JSON，则将原始数据作为字符串
					params = string(body)
				}
			}
		} else {
			// 对于GET请求，记录查询参数
			params = c.Request.URL.Query()
		}

		// 创建自定义ResponseWriter以捕获响应
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw

		// 处理请求
		c.Next()

		// 获取响应信息
		responseBody := blw.body.String()
		var response interface{}
		if strings.HasPrefix(c.Writer.Header().Get("Content-Type"), "application/json") {
			if err := json.Unmarshal([]byte(responseBody), &response); err != nil {
				response = responseBody
			}
		} else {
			response = responseBody
		}

		// 截断响应结果
		var truncatedResponse interface{}
		if responseStr, ok := response.(string); ok {
			if len(responseStr) > 1000 {
				truncatedResponse = responseStr[:1000]
			} else {
				truncatedResponse = responseStr
			}
		} else {
			responseBytes, _ := json.Marshal(response)
			if len(responseBytes) > 1000 {
				truncatedResponse = string(responseBytes[:1000])
			} else {
				truncatedResponse = response
			}
		}

		// 获取状态码
		status := c.Writer.Status()

		// 异步记录操作日志，传递复制的数据以避免并发问题
		go func(path, method string, params, response interface{}, ip, userAgent string, status int) {
			_ = operationLogService.CreateLogFromRequest(
				path,
				method,
				params,
				response,
				ip,
				userAgent,
				status,
			)
		}(path, method, params, truncatedResponse, ip, userAgent, status)
	}
}

// bodyLogWriter 用于捕获响应体
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}
