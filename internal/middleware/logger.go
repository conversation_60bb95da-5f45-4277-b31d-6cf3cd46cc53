package middleware

import (
	"bytes"
	"goadmin/pkg/ctxutil"
	"goadmin/pkg/logger"
	"io"
	"time"

	"github.com/gin-gonic/gin"
)

func RequestResponseLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 使用带有请求ID的上下文
		ctx := ctxutil.GetRequestContext(c)

		// 异步记录请求信息
		go func() {
			// Log request parameters
			bodyBytes, _ := io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes)) // Restore the body for further use

			logger.Info(ctx, "请求路径: %s，请求头信息: %+v", c.Request.URL.Path, c.Request.Header)
			logger.LogRequest(ctx, string(bodyBytes))
		}()

		// Capture the response
		responseWriter := &responseCapture{ResponseWriter: c.Writer, body: &bytes.Buffer{}}
		c.Writer = responseWriter

		startTime := time.Now()
		c.Next()
		duration := time.Since(startTime)

		// 异步记录响应信息
		go func() {
			// Log response parameters
			logger.LogResponse(ctx, responseWriter.body.String())

			// Log additional details like status and duration
			logger.Info(ctx, "Status: %d, Duration: %v", c.Writer.Status(), duration)
		}()
	}
}

type responseCapture struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (r *responseCapture) Write(b []byte) (int, error) {
	r.body.Write(b)
	return r.ResponseWriter.Write(b)
}
