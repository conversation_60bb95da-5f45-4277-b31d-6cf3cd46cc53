package model

import "time"

// ThirdAPIKey 第三方API密钥模型
type ThirdAPIKey struct {
	ID          int64      `json:"id" gorm:"primary_key"`
	SecretID    string     `json:"secret_id" gorm:"column:secret_id;uniqueIndex;size:32"`
	SecretKey   string     `json:"secret_key" gorm:"column:secret_key;size:64"`
	Name        string     `json:"name" gorm:"column:name;size:100"`      // 密钥名称/描述
	Status      int8       `json:"status" gorm:"column:status;default:1"` // 1:启用 2:禁用
	Permissions string     `json:"permissions" gorm:"column:permissions"` // 权限列表，JSON格式
	LastUsedAt  *time.Time `json:"last_used_at,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"` // 密钥过期时间
}

// TableName 表名
func (ThirdAPIKey) TableName() string {
	return "third_api_keys"
}

// APIKeyPermissions 权限定义
type APIKeyPermissions struct {
	Recharge bool `json:"recharge"` // 充值权限
	Query    bool `json:"query"`    // 查询权限
}
