package model

import "time"

// CardKey 卡密模型
type CardKey struct {
	ID           int64      `json:"id" gorm:"primarykey"`
	<PERSON><PERSON><PERSON>      string     `json:"card_key" gorm:"column:card_key;uniqueIndex"`
	Status       int8       `json:"status"`
	Type         int8       `json:"type"`
	Value        int        `json:"value"`
	MaxDevices   int        `json:"max_devices" gorm:"default:1"` // 支持的最大设备数量
	CreatedAt    time.Time  `json:"created_at"`
	ExpiredAt    *time.Time `json:"expired_at,omitempty"`
	UsedAt       *time.Time `json:"used_at,omitempty"`
	ParentID     *string    `json:"parent_id,omitempty"`     // 充值目标卡密ID
	RechargeTime *time.Time `json:"recharge_time,omitempty"` // 充值时间
}

// TableName 表名
func (CardKey) TableName() string {
	return "card_keys"
}
