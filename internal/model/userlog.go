package model

import (
	"encoding/json"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// UserLog 用户日志
type UserLog struct {
	ID        int64     `json:"id" gorm:"primarykey"`
	UserID    string    `json:"user_id"`
	RobotID   string    `json:"robot_id"`
	PlanName  string    `json:"plan_name"`
	Claims    string    `json:"claims"`
	CreatedAt time.Time `json:"created_at"`
}

// SetClaims 设置Claims
func (u *UserLog) SetClaims(claims jwt.MapClaims) error {
	data, err := json.Marshal(claims)
	if err != nil {
		return err
	}
	u.Claims = string(data)
	return nil
}

// TableName 表名
func (UserLog) TableName() string {
	return "user_logs"
}
