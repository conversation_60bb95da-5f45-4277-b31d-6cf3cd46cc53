package model

import (
	"time"
)

// OperationLog API操作日志
type OperationLog struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Path      string    `gorm:"type:varchar(255);not null" json:"path"`  // 请求路径
	Method    string    `gorm:"type:varchar(10);not null" json:"method"` // 请求方法
	Params    string    `gorm:"type:text" json:"params"`                 // 请求参数
	Response  string    `gorm:"type:text" json:"response"`               // 响应结果
	IP        string    `gorm:"type:varchar(64)" json:"ip"`              // 请求IP
	UserAgent string    `gorm:"type:varchar(255)" json:"user_agent"`     // 用户代理
	Status    int       `gorm:"type:int" json:"status"`                  // HTTP响应状态码
	Code      int       `gorm:"type:int;index" json:"code"`              // 业务响应码
	CreatedAt time.Time `json:"created_at"`
}

// TableName 表名
func (OperationLog) TableName() string {
	return "operation_logs"
}
