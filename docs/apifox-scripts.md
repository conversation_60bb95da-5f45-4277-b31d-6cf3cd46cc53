# ApiFox 测试脚本

## 预执行脚本（签名生成）

在ApiFox的接口设置中，添加以下预执行脚本：

```javascript
// GoAdmin 第三方API签名生成脚本
// 在ApiFox的"预执行脚本"中使用

// 配置您的密钥信息
const SECRET_ID = "AKID1234567890abcdef1234567890ab";  // 替换为您的SecretId
const SECRET_KEY = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";  // 替换为您的SecretKey

// 获取当前时间戳
const timestamp = Math.floor(Date.now() / 1000);

// 获取请求信息
const method = pm.request.method;
const url = pm.request.url;
const uri = url.getPath();

// 获取请求体参数
let params = {};
if (pm.request.body && pm.request.body.mode === 'raw') {
    try {
        const bodyData = JSON.parse(pm.request.body.raw);
        params = bodyData;
    } catch (e) {
        console.log("解析请求体失败:", e);
    }
}

// 添加时间戳到参数中
params.timestamp = timestamp.toString();

// 生成签名的辅助函数
function generateSignature(method, uri, params, secretKey, timestamp) {
    // 1. 对参数进行排序
    const sortedKeys = Object.keys(params).sort();
    
    // 2. 构造查询字符串
    const queryString = sortedKeys
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');
    
    // 3. 构造待签名字符串
    const stringToSign = `${method.toUpperCase()}\n${uri}\n${queryString}`;
    
    console.log("待签名字符串:");
    console.log(stringToSign);
    
    // 4. 生成签名 (使用CryptoJS库)
    const signature = CryptoJS.HmacSHA256(stringToSign, secretKey).toString();
    
    return signature;
}

// 生成签名
const signature = generateSignature(method, uri, params, SECRET_KEY, timestamp);

// 设置请求头
pm.request.headers.add({
    key: "X-Secret-Id",
    value: SECRET_ID
});

pm.request.headers.add({
    key: "X-Timestamp", 
    value: timestamp.toString()
});

pm.request.headers.add({
    key: "X-Signature",
    value: signature
});

pm.request.headers.add({
    key: "Content-Type",
    value: "application/json"
});

// 输出调试信息
console.log("=== GoAdmin API 签名信息 ===");
console.log("SecretId:", SECRET_ID);
console.log("时间戳:", timestamp);
console.log("签名:", signature);
console.log("请求方法:", method);
console.log("请求路径:", uri);
console.log("请求参数:", JSON.stringify(params, null, 2));
console.log("========================");

// 设置环境变量（可选，用于在其他地方引用）
pm.environment.set("goadmin_secret_id", SECRET_ID);
pm.environment.set("goadmin_timestamp", timestamp);
pm.environment.set("goadmin_signature", signature);
```

## 后执行脚本（响应验证）

在ApiFox的"后执行脚本"中添加以下代码：

```javascript
// 后执行脚本 - 验证响应和错误处理
pm.test("HTTP状态码为200", function () {
    pm.response.to.have.status(200);
});

pm.test("响应格式正确", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('code');
    pm.expect(jsonData).to.have.property('message');
});

pm.test("业务处理结果", function () {
    const jsonData = pm.response.json();
    
    if (jsonData.code === 0) {
        console.log("✅ 充值成功");
        pm.expect(jsonData).to.have.property('data');
    } else {
        console.log("❌ 充值失败，错误码:", jsonData.code);
        console.log("错误信息:", jsonData.message);
        
        // 根据错误码提供处理建议
        switch (jsonData.code) {
            case 1000:
                console.log("💡 建议: 服务器内部错误，重试或联系技术支持");
                break;
            case 1001:
                console.log("💡 建议: 检查请求参数格式和必填字段");
                console.log("   - 确保 target_card_key 和 new_card_key 都已提供");
                console.log("   - 检查参数值是否为有效字符串");
                break;
            case 1002:
                console.log("💡 建议: 检查认证信息");
                console.log("   - 验证 SecretId 是否正确");
                console.log("   - 检查签名算法实现");
                console.log("   - 确保系统时间同步（误差不超过5分钟）");
                console.log("   - 确认 API 密钥未过期或被禁用");
                break;
            case 2001:
                console.log("💡 建议: 检查卡密是否正确");
                console.log("   - 确认目标卡密存在");
                console.log("   - 确认充值卡密存在");
                break;
            case 2002:
                console.log("💡 建议: 使用未使用的卡密");
                console.log("   - 充值卡密已被使用，请使用新的卡密");
                break;
            case 2003:
                console.log("💡 建议: 使用有效期内的卡密");
                console.log("   - 充值卡密已过期，请使用有效期内的卡密");
                break;
            case 2004:
                console.log("💡 建议: 联系管理员解除卡密禁用");
                console.log("   - 卡密被管理员禁用，需要联系管理员处理");
                break;
            case 6001:
                console.log("💡 建议: 联系管理员配置充值权限");
                console.log("   - API 密钥没有充值权限");
                console.log("   - 需要管理员为 API 密钥添加充值权限");
                break;
            default:
                console.log("💡 建议: 未知错误，联系技术支持");
                console.log("   - 错误码:", jsonData.code);
                console.log("   - 错误信息:", jsonData.message);
        }
    }
});

// 输出完整响应信息
console.log("=== 完整响应信息 ===");
console.log("HTTP状态码:", pm.response.code);
console.log("响应时间:", pm.response.responseTime + "ms");
console.log("响应大小:", pm.response.responseSize + " bytes");
console.log("响应内容:", JSON.stringify(pm.response.json(), null, 2));
console.log("==================");

// 保存响应数据到环境变量（可选）
const responseData = pm.response.json();
pm.environment.set("last_response_code", responseData.code);
pm.environment.set("last_response_message", responseData.message);
```

## 环境变量配置

### 方式1：在脚本中直接配置
直接在预执行脚本中修改 `SECRET_ID` 和 `SECRET_KEY` 的值。

### 方式2：使用环境变量
1. 在ApiFox的环境管理中添加以下变量：
   - `goadmin_secret_id`: 您的SecretId
   - `goadmin_secret_key`: 您的SecretKey
   - `goadmin_base_url`: API基础URL

2. 使用环境变量的预执行脚本：
```javascript
// 从环境变量获取配置
const SECRET_ID = pm.environment.get("goadmin_secret_id");
const SECRET_KEY = pm.environment.get("goadmin_secret_key");
const BASE_URL = pm.environment.get("goadmin_base_url") || "https://your-domain.com";

if (!SECRET_ID || !SECRET_KEY) {
    throw new Error("请在环境变量中设置 goadmin_secret_id 和 goadmin_secret_key");
}

// 其余代码与上面相同...
```

## 使用步骤

1. **创建接口**：
   - 方法：POST
   - URL：`{{goadmin_base_url}}/api/v1/third/recharge`
   - 请求体：JSON格式

2. **设置请求体**：
```json
{
  "target_card_key": "TARGET_CARD_KEY",
  "new_card_key": "SOURCE_CARD_KEY"
}
```

3. **添加脚本**：
   - 将预执行脚本粘贴到"预执行脚本"选项卡
   - 将后执行脚本粘贴到"后执行脚本"选项卡

4. **配置密钥**：
   - 修改脚本中的密钥信息，或
   - 在环境变量中配置密钥

5. **发送请求**：
   - 点击"发送"按钮
   - 查看控制台输出的调试信息

## 调试技巧

1. **查看签名过程**：预执行脚本会输出待签名字符串，便于调试
2. **错误分析**：后执行脚本会根据错误码提供具体的处理建议
3. **响应分析**：完整的响应信息会在控制台中显示
4. **环境变量**：可以保存响应数据供其他接口使用
